/*
 * Xgm00301T03Bean.java
 *
 * 著作権  ：Copyright Japan System Techniques Co., Ltd. All Rights Reserved.
 * 会社名  ：日本システム技術株式会社
 *
 */
package com.jast.gakuen.gk.xg.bean;

import com.jast.gakuen.common.gh.constant.code.OutputKbn;
import com.jast.gakuen.common.pk.constant.code.BunmenKbn;
import com.jast.gakuen.core.common.BaseSubBean;
import com.jast.gakuen.core.common.SessionInfo;
import com.jast.gakuen.core.common.constant.AsyncExecConst;
import com.jast.gakuen.core.common.constant.FileTypeConst;
import com.jast.gakuen.core.common.constant.code.PrdKbn;
import com.jast.gakuen.core.common.dto.OptionDTO;
import com.jast.gakuen.core.common.dto.OrdConditionDTO;
import com.jast.gakuen.core.common.exception.DataException;
import com.jast.gakuen.core.common.exception.GakuenException;
import com.jast.gakuen.core.common.service.IOptionService;
import com.jast.gakuen.core.common.service.IOrdService;
import com.jast.gakuen.core.common.util.Message;
import com.jast.gakuen.core.common.util.Transporter;
import com.jast.gakuen.core.common.util.UtilDialog;
import com.jast.gakuen.core.common.util.UtilFaces;
import com.jast.gakuen.core.common.util.UtilLocalization;
import com.jast.gakuen.core.common.util.UtilStr;
import com.jast.gakuen.core.gk.annotation.GkBackingBean;
import com.jast.gakuen.core.gk.annotation.GkWindowOpen;
import com.jast.gakuen.gk.gh.dto.Ghb201DTO02;
import com.jast.gakuen.gk.gh.dto.Ghb201DTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO02;
import com.jast.gakuen.gk.gh.dto.Ghd008CollectiveOutputDTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008ConditionDTO01;
import com.jast.gakuen.gk.gh.dto.Ghd008ConditionDTO02;
import com.jast.gakuen.gk.gh.dto.Ghd008ConditionDTO06;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO03;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO04;
import com.jast.gakuen.gk.gh.dto.Ghd008DTO05;
import com.jast.gakuen.gk.xg.service.IXgm003Service;
import com.jast.gakuen.gk.pk.dto.PkPrinterConditionDTO02;
import com.jast.gakuen.gk.pk.dto.PkPrinterDTO;
import com.jast.gakuen.gk.pk.service.IPkzPrinterService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import javax.enterprise.context.SessionScoped;
import javax.inject.Inject;
import javax.inject.Named;
import lombok.Getter;
import lombok.Setter;
import org.primefaces.context.RequestContext;

/**
 * 学生納付金通知書(学生指定)
 *
 * <AUTHOR> System Techniques Co.,Ltd.
 */
@Named
@SessionScoped
public class Xgm00301T03Bean extends BaseSubBean {

	/**
	 * 学生納付金通知書ービス
	 */
	@Inject
	protected IXgm003Service xgm003Service;

	/**
	 * プリンタサービス
	 */
	@Inject
	protected IPkzPrinterService printerService;

	/**
	 * 学生納付金通知書出力
	 */
	@Inject
	@Getter
	protected Xgm00301Bean xgm00301Bean;

	/**
	 * 出力内容設定 条件DTO
	 */
	@Getter
	protected Ghd008ConditionDTO01 conditionOutput = new Ghd008ConditionDTO01();

	/**
	 * 入出力項目並び順サービス
	 */
	@Inject
	protected IOrdService ordService;

	/**
	 * オプションサービス
	 */
	@Inject
	protected IOptionService optionService;

	/**
	 * トランスポーター
	 */
	protected final Transporter transporter = new Transporter();

	/**
	 * 学生指定 検索条件DTO
	 */
	@Getter
	protected final Ghd008ConditionDTO06 condition = new Ghd008ConditionDTO06();

	/**
	 * 学生納付金通知書出力(データ)DTO(非同期処理)
	 */
	@Getter
	protected final Ghd008CollectiveOutputDTO02 collectiveOutputData = new Ghd008CollectiveOutputDTO02();

	/**
	 * 学生納付金通知書出力(帳票)DTO(非同期処理)
	 */
	@Getter
	protected final Ghd008CollectiveOutputDTO03 collectiveOutputReport = new Ghd008CollectiveOutputDTO03();

	/**
	 * 納付金リスト
	 */
	@Getter
	protected List<Ghd008DTO03> payList = new ArrayList<>();

	/**
	 * 選択された納付金リスト
	 */
	@Getter
	@Setter
	protected List<Ghd008DTO03> selectedPayList = new ArrayList<>();

	/**
	 * 学生氏名
	 */
	@Getter
	protected String gakseiName;

	/**
	 * 初期表示処理
	 *
	 * @param conditionOutput 出力内容指定DTO
	 * @throws Exception 例外
	 */
	public void doInit(final Ghd008ConditionDTO01 conditionOutput) throws Exception {

		// 出力内容設定情報
		this.conditionOutput = conditionOutput;

		// ------------------------------
		// 初期値設定
		// ------------------------------
		// 納付金リスト
		this.getPayList().clear();
		this.getPayList().addAll(selectedPayList);

		// ------------------------------
		// オプション情報を取得
		// ------------------------------
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.get(option);

	}

	/**
	 * 「学生検索」ボタン押下
	 *
	 * @return 子画面ＩＤ
	 */
	@GkBackingBean
	@GkWindowOpen(other = "{\"openId\":\"Ghd00802T04:openChildWindow\"}")
	public String doOpenSearchGakseki() {

		// ・ 検索モード ： 0（単一検索モード）
		Ghb201DTO02 param = new Ghb201DTO02();
		param.setSearchMode("0");

		UtilDialog.setDialogParameters(param);
		return "Ghb20101";
	}

	/**
	 * 学生検索画面クローズ
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doReceiveGaksekiCd() throws Exception {
		List<Ghb201DTO03> rtnList = UtilDialog.getDialogParameters();
		if (!rtnList.isEmpty()) {
			this.condition.setGaksekiCd(rtnList.get(0).getGaksekiCd());
			this.doGetGakName();
		}
	}

	/**
	 * 学生氏名の取得
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doGetGakName() throws Exception {
		this.gakseiName = null;
		if (UtilStr.isEmpty(this.condition.getGaksekiCd())) {
			return;
		}
		Ghd008DTO05 paramDto = new Ghd008DTO05();
		paramDto.setSkijunDate(this.conditionOutput.getSkijunDate());
		paramDto.setGaksekiCd(this.condition.getGaksekiCd());
		Ghd008DTO05 rtnDto = this.xgm003Service.getGakseiName(paramDto);
		if (rtnDto != null) {
			this.condition.setKanriNo(rtnDto.getKanriNo());
			this.gakseiName = rtnDto.getGakseiName();
		}
	}

	/**
	 * 検索処理
	 *
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doSearch(final Ghd008ConditionDTO02 conditionHeader) throws Exception {

		if (UtilStr.isEmpty(this.condition.getGaksekiCd())) {
			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 36,
					UtilLocalization.getItemValue("pkbGakReki.gaksekiCd.0.label")));
		}

		Ghd008DTO05 gakParamDto = new Ghd008DTO05();
		gakParamDto.setSkijunDate(conditionOutput.getSkijunDate());
		gakParamDto.setGaksekiCd(this.condition.getGaksekiCd());
		Ghd008DTO05 gakRtnDto = this.xgm003Service.checkGaksekiCd(gakParamDto);
		if (gakRtnDto != null) {
			this.condition.setProductKbn(gakRtnDto.getProductKbn());
			this.condition.setGakSotKbn(gakRtnDto.getGakSotKbn());
			this.condition.setKanriNo(gakRtnDto.getKanriNo());
			this.gakseiName = gakRtnDto.getGakseiName();
		}

		// 納付金リストの編集
		Ghd008DTO04 paramDto = new Ghd008DTO04();

		// 文面区分
		String[] outBunmenArray = this.conditionOutput.getOutBunmen().split(",");
		paramDto.setBunmenKbn(outBunmenArray[0]);

		// 発行日付
		paramDto.setHakkoDate(this.conditionOutput.getHakkoDate());
		// 管理番号
		paramDto.setKanriNo(this.condition.getKanriNo());

		paramDto.setOutputNofusho(false);
		paramDto.setOutputJidoHikiotoshi(false);
		paramDto.setOutputShutryokTaishoGai(false);
		for (String outputKbn : conditionHeader.getOutputKbn()) {
			if (OutputKbn.Nofusho.getCode().equals(outputKbn)) {
				// 納付書抽出フラグ
				paramDto.setOutputNofusho(true);
			} else if (OutputKbn.JidoHikiotoshi.getCode().equals(outputKbn)) {
				// 自動引落抽出フラグ
				paramDto.setOutputJidoHikiotoshi(true);
			} else if (OutputKbn.ShutryokTaishoGai.getCode().equals(outputKbn)) {
				// 出力対象外抽出フラグ
				paramDto.setOutputShutryokTaishoGai(true);
			}
		}

		// すべて未選択は、すべて選択とする
		if (!paramDto.isOutputNofusho() && !paramDto.isOutputJidoHikiotoshi() && !paramDto.isOutputShutryokTaishoGai()) {
			paramDto.setOutputNofusho(true);
			paramDto.setOutputJidoHikiotoshi(true);
			paramDto.setOutputShutryokTaishoGai(true);
		}

		// 納入期限日延納優先
		paramDto.setEnnoYusenPayLimit(this.conditionOutput.isEnnoYusenPayLimit());
		// 自動引落日延納優先
		paramDto.setEnnoYusenHikiotoshi(this.conditionOutput.isEnnoYusenHikiotoshi());

		List<Ghd008DTO03> payWList = this.xgm003Service.getPayWList(paramDto);

		// 対象データが存在しない場合、エラーとする
		if (payWList.isEmpty()) {
			throw new DataException(new Message(PrdKbn.PK.getCode(), Message.TypeCode.E, 222, UtilLocalization.getItemValue("common.specifiedStu.0.label"),
					UtilLocalization.getItemValue("common.ghePayw.0.label")));
		}

		this.payList.addAll(payWList);
	}

	/**
	 * クリア処理
	 *
	 * @throws Exception 例外
	 */
	@GkBackingBean
	public void doClear() throws Exception {
		// 納付金リストの初期化
		this.payList = new ArrayList<>();
		this.selectedPayList = new ArrayList<>();
	}

	/**
	 * 納付金通知書帳票出力の共通処理
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	public void outputReportProc(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {
		try {
			// 相関チェック
			this.checkProc();

			// 納付金通知書出力(帳票)DTO(非同期処理)に値をセットする
			this.setCollectiveOutputReport(fileType, conditionHeader);

		} catch (Exception e) {
			// エラーが発生した場合は、RequestContextにfalseを設定する
			RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), false);
			throw e;
		}

		// オプション情報に保存
		saveOption();
	}

	/**
	 * 納付金通知書出力(帳票)DTO(非同期処理)に実行情報を設定する
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @param dto 非同期処理共通DTO
	 * @throws Exception 例外
	 */
	protected void setCollectiveOutput(final FileTypeConst fileType,
			final Ghd008ConditionDTO02 conditionHeader,
			final Ghd008CollectiveOutputDTO01 dto)
			throws Exception {

		// 【一括出力固有パラメータ】
		dto.setFileTypeConst(fileType);
		dto.setDelDuplicateFlg(conditionHeader.isDelDuplicateFlg());

		// 【機能固有パラメータ】
		String[] outBunmenArray = this.conditionOutput.getOutBunmen().split(",");
		String bunmenKbn = outBunmenArray[0];
		int reportSubNo = Integer.parseInt(outBunmenArray[1]);

		// if (BunmenKbn.Gh2rnou.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GH2RNOU);
		// } else if (BunmenKbn.Gh3rnou.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GH3RNOU);
		// } else if (BunmenKbn.Ghsnou.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GHSNOU);
		// } else if (BunmenKbn.Ghitnou.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GHITNOU);
		// } else if (BunmenKbn.Ghsou.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GHSOU);
		// } else if (BunmenKbn.Ghtok.getCode().equals(bunmenKbn)) {
		// 	dto.setFileId(Xgm00301Bean.FILE_ID_GHTOK);
		// }
		dto.setBunmenKbn(bunmenKbn);
		dto.setReportSubNo(reportSubNo);

		dto.setHakkoDate(this.conditionOutput.getHakkoDate());
		dto.setSkijunDate(this.conditionOutput.getSkijunDate());
		dto.setOutputPrinter(this.conditionOutput.getOutputPrinter());
		dto.setChecklistOutput(this.conditionOutput.isChecklistOutput());
		dto.setAddAtesaki(this.conditionOutput.getAddAtesaki());
		dto.setAddHoshoninSbt(this.conditionOutput.getAddHoshoninSbt());
		dto.setNoOutput(this.conditionOutput.isNoOutput());
		dto.setPayLimitDateRangeFrom(this.conditionOutput.getPayLimitDateRangeFrom());
		dto.setPayLimitDateRangeTo(this.conditionOutput.getPayLimitDateRangeTo());
		dto.setEnnoYusenPayLimit(this.conditionOutput.isEnnoYusenPayLimit());
		dto.setEnnoYusenHikiotoshi(this.conditionOutput.isEnnoYusenHikiotoshi());

		dto.setOutAtenaLabel(this.conditionOutput.isOutAtenaLabel());
		dto.setAtenaYubinShow(this.conditionOutput.isAtenaYubinShow());
		dto.setAtenaBarcodeOutput(this.conditionOutput.isAtenaBarcodeOutput());
		dto.setAtenaOutputStartPosition(this.conditionOutput.getAtenaOutputStartPosition());
		dto.setAtnaOutputPrinter(this.conditionOutput.getAtnaOutputPrinter());
		dto.setAtenaAddAtesaki(this.conditionOutput.getAtenaAddAtesaki());
		dto.setAtenaAddHoshoninSbt(this.conditionOutput.getAtenaAddHoshoninSbt());
		dto.setAtenaOutRenban(this.conditionOutput.isAtenaOutRenban());
		dto.setAtenaBiko(this.conditionOutput.getAtenaBiko());
		dto.setTubNo(4);

		dto.setPayList(this.selectedPayList);
		dto.setHakkoTgtZengakuMino(true);
		dto.setHakkoTgtIchibuMino(true);

		dto.setGaksekiCdList(new ArrayList<>(Arrays.asList(this.condition.getGaksekiCd())));
	}

	/**
	 * 納付金通知書出力(帳票)DTO(非同期処理)に実行情報を設定する
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	protected void setCollectiveOutputReport(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {

		this.setCollectiveOutput(fileType, conditionHeader, this.collectiveOutputReport);

		RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), true);
	}

	/**
	 * 納付金通知書データ出力の共通処理
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	public void outputDatProc(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {
		try {
			// 相関チェック
			this.checkProc();
			// 納付金通知書出力(データ)DTO(非同期処理)に値をセットする
			this.setCollectiveOutputData(fileType, conditionHeader);

		} catch (Exception e) {
			// エラーが発生した場合は、RequestContextにfalseを設定する
			RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), false);
			throw e;
		}

		// オプション情報に保存
		saveOption();
	}

	/**
	 * 納付金通知書出力(データ)DTO(非同期処理)に実行情報を設定する
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	protected void setCollectiveOutputData(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {

		this.setCollectiveOutput(fileType, conditionHeader, this.collectiveOutputData);

		// 【入出力項目並び順】
		OrdConditionDTO con = new OrdConditionDTO();
		con.setUserId(SessionInfo.getSessionInfo().getUserInfo().getUserId());
		// con.setFileId(Xgm00301Bean.FILE_ID_DATA);
		con.setOrdNo(conditionHeader.getOrdNo());
		this.collectiveOutputData.setOrdDto(this.ordService.getIoOrder(con));

		RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), true);
	}

	/**
	 * 納付金通知書帳票印刷の共通処理
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	public void outputPrintProc(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {

		try {
			// 相関チェック
			this.checkProcForPrint();
			this.checkProc();
			// 出欠簿(印刷)DTO(非同期処理)に値をセットする
			this.setCollectiveOutputPrint(fileType, conditionHeader);

		} catch (Exception e) {
			// エラーが発生した場合は、RequestContextにfalseを設定する
			RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), false);
			throw e;
		}
		// オプション情報に保存
		saveOption();
	}

	/**
	 * 納付金通知書出力(印刷)DTO(非同期処理)に実行情報を設定する
	 *
	 * @param fileType 出力ファイル形式
	 * @param conditionHeader ヘッダ部条件
	 * @throws Exception 例外
	 */
	protected void setCollectiveOutputPrint(final FileTypeConst fileType, final Ghd008ConditionDTO02 conditionHeader) throws Exception {

		this.setCollectiveOutputReport(fileType, conditionHeader);

		// ログインユーザ情報を取得
		SessionInfo sessionInfo = SessionInfo.getSessionInfo();
		// プリントIDを取得する
		PkPrinterConditionDTO02 param = new PkPrinterConditionDTO02(sessionInfo.getLoginUser().getUserId());
		PkPrinterDTO rtnDto = printerService.getUserPrinter(param);
		if (rtnDto == null) {
			throw new GakuenException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 77));
		} else {
			this.collectiveOutputReport.setPrinter(rtnDto.getPrtId());
		}

		RequestContext.getCurrentInstance().addCallbackParam(AsyncExecConst.BeforeResultKey.getCode(), true);
	}

	/**
	 * オプション情報に保存
	 *
	 * @throws Exception 例外
	 */
	protected void saveOption() throws Exception {
		this.xgm00301Bean.saveOption();

		// オプション情報を保存
		OptionDTO option = new OptionDTO();
		option.setFormId(UtilFaces.getFormId());
		option.setBaseDto(this.condition);
		this.optionService.set(option);
	}

	/**
	 * 相関チェック（印刷時固有）
	 *
	 * @throws Exception 例外
	 */
	protected void checkProcForPrint() throws Exception {

		// 出力内容設定.出力プリンタが未選択であればエラー
		if (UtilStr.isEmpty(conditionOutput.getOutputPrinter())) {
			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 46,
					UtilLocalization.getItemValue("common.outputPrinter.0.label")));

		}

		// 出力内容設定.宛名ラベル連動出力がONの場合、
		// 出力内容設定.宛名ラベル出力プリンタが未選択であればエラー
		if (conditionOutput.isOutAtenaLabel()) {
			if (UtilStr.isEmpty(conditionOutput.getAtnaOutputPrinter())) {
				throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 46,
						UtilLocalization.getItemValue("ghd008.outAtenaLabelPrinter.0.label")));
			}
		}
	}

	/**
	 * 相関チェック
	 *
	 * @throws Exception 例外
	 */
	protected void checkProc() throws Exception {

		// 納付金一覧が1件も選択されていなければエラー
		if (this.selectedPayList.isEmpty()) {
			throw new DataException(new Message(PrdKbn.SY.getCode(), Message.TypeCode.E, 40,
					UtilLocalization.getItemValue("common.pay.0.label")));
		}
	}

	/**
	 * 延納完了の納付金選択判定
	 * 
	 * @return true/false
	 */
	protected boolean isExistEnnoKanryo() {
		for (Ghd008DTO03 dto : this.selectedPayList) {
			if (dto.isEnnoKanryoFlg()) {
				return true;
			}
		}
		return false;
	}
}
